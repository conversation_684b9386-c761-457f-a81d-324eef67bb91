package video

import (
	"context"
	"errors"
	"fmt"

	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	ErrVideoNotFound = errors.New("视频未找到")
)

// Repository 定义了视频的数据访问接口
type Repository interface {
	Create(ctx context.Context, video *Video) error
	FindByID(ctx context.Context, id string) (*Video, error)
	Update(ctx context.Context, video *Video) error
	FindOneAndUpdateToProcessing(ctx context.Context) (*Video, error)
	UpdateCategory(ctx context.Context, oldCategoryID, newCategoryID primitive.ObjectID) error
	UpdateAdvertiser(ctx context.Context, oldAdvertiserID, newAdvertiserID primitive.ObjectID) (int64, error)
	IncrementStats(ctx context.Context, videoID string, views, likes, collections, completions int64) error
	Find(ctx context.Context, filter VideoFilter) ([]Video, int64, error)
	DeleteByID(ctx context.Context, id primitive.ObjectID) error
	UpdateSelective(ctx context.Context, id primitive.ObjectID, updateData bson.M) error
	UpdateSelectiveWithStatusCheck(ctx context.Context, id primitive.ObjectID, expectedStatus string, updateData bson.M) error
	GetAggregatedStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error)
}

type repository struct {
	collection *gomongo.MongoCollection
}

// NewRepository 创建一个新的视频仓库实例
func NewRepository() Repository {
	collection := database.GetCollection("realmaster_video", "video_videos")
	return &repository{collection: collection}
}

// Create 向数据库中插入一个新的视频文档
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Create(ctx context.Context, video *Video) error {
	// 使用传入的context（可能是SessionContext）
	_, err := r.collection.InsertOne(ctx, video)
	if err != nil {
		logger.Log.Error("在数据库中创建视频失败", logger.Error(err))
		return fmt.Errorf("仓库层: 创建视频失败: %w", err)
	}
	return nil
}

// FindByID 通过ID查找单个视频
func (r *repository) FindByID(ctx context.Context, id string) (*Video, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("仓库层: 无效的ID格式: %w", err)
	}

	var video Video
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&video)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrVideoNotFound
		}
		logger.Log.Error("通过ID查找视频失败", logger.Error(err), logger.String("id", id))
		return nil, fmt.Errorf("仓库层: 通过ID查找视频失败: %w", err)
	}
	return &video, nil
}

// Update 更新一个已存在的视频文档
// 它会智能地使用 $set 和 $unset 来更新字段
func (r *repository) Update(ctx context.Context, video *Video) error {
	filter := bson.M{"_id": video.ID}

	// 手动构建更新文档，以精确控制字段
	setFields := bson.M{
		"tl":      video.Title,
		"dsc":     video.Description,
		"dur":     video.Duration,
		"uldId":   video.UploaderID,
		"tags":    video.Tags,
		"st":      video.Status,
		"prop":    video.PropertyIDs,
		"ExtUrl":  video.ExternalURL,
		"stat":    video.Stats,
		"procErr": video.ProcessingError,
		// 移除手动时间戳字段，gomongo会自动管理_ts和_mt
		// "ts":                video.CreatedAt,
		// "mt":                video.UpdatedAt,
		"pts":               video.PublishedAt,
		"manifestsBaseName": video.ManifestsBaseName,
	}

	// 只有当 ObjectID 非空时才更新，避免写入零值
	if !video.CategoryID.IsZero() {
		setFields["catId"] = video.CategoryID
	}
	if !video.ClientID.IsZero() {
		setFields["ClntId"] = video.ClientID
	}

	// Handle setting/unsetting of goupload paths
	unsetFields := bson.M{}
	if video.DraftVideoGouploadPath == "" {
		unsetFields["draftVideoGouploadPath"] = ""
	} else {
		setFields["draftVideoGouploadPath"] = video.DraftVideoGouploadPath
	}
	if video.DraftThumbGouploadPath == "" {
		unsetFields["draftThumbGouploadPath"] = ""
	} else {
		setFields["draftThumbGouploadPath"] = video.DraftThumbGouploadPath
	}
	if video.FinalVideoGouploadPath == "" {
		unsetFields["finalVideoGouploadPath"] = ""
	} else {
		setFields["finalVideoGouploadPath"] = video.FinalVideoGouploadPath
	}
	if video.FinalThumbGouploadPath == "" {
		unsetFields["finalThumbGouploadPath"] = ""
	} else {
		setFields["finalThumbGouploadPath"] = video.FinalThumbGouploadPath
	}

	// Ensure the old temp path is also removed for cleanup
	unsetFields["tempPath"] = ""

	update := bson.M{"$set": setFields}
	if len(unsetFields) > 0 {
		update["$unset"] = unsetFields
	}

	opts := options.Update().SetUpsert(false)
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		logger.Log.Error("更新视频失败", logger.Error(err), logger.String("id", video.ID.Hex()))
		return fmt.Errorf("仓库层: 更新视频失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return ErrVideoNotFound
	}

	return nil
}

// UpdateSelective 更新一个已存在的视频文档中的指定字段
func (r *repository) UpdateSelective(ctx context.Context, id primitive.ObjectID, updateData bson.M) error {
	filter := bson.M{"_id": id}

	// 智能地处理 $set 和 $unset
	update := bson.M{}
	setFields := bson.M{}
	unsetFields := bson.M{}

	for key, value := range updateData {
		if value == nil || value == "" {
			// 如果传入的值是nil或空字符串，我们认为是想删除该字段
			unsetFields[key] = ""
		} else {
			setFields[key] = value
		}
	}

	if len(setFields) > 0 {
		update["$set"] = setFields
	}
	if len(unsetFields) > 0 {
		update["$unset"] = unsetFields
	}

	if len(update) == 0 {
		// 没有需要更新的字段
		return nil
	}

	opts := options.Update().SetUpsert(false)
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		logger.Log.Error("选择性更新视频失败", logger.Error(err), logger.String("id", id.Hex()))
		return fmt.Errorf("仓库层: 选择性更新视频失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return ErrVideoNotFound
	}

	return nil
}

// UpdateSelectiveWithStatusCheck 原子性地更新视频，同时检查当前状态
// 这可以防止并发修改导致的状态不一致
func (r *repository) UpdateSelectiveWithStatusCheck(ctx context.Context, id primitive.ObjectID, expectedStatus string, updateData bson.M) error {
	// 在filter中包含状态检查，确保只有在预期状态下才能更新
	filter := bson.M{
		"_id": id,
		"st":  expectedStatus,
	}

	// 智能地处理 $set 和 $unset
	update := bson.M{}
	setFields := bson.M{}
	unsetFields := bson.M{}

	for key, value := range updateData {
		if value == nil || value == "" {
			unsetFields[key] = ""
		} else {
			setFields[key] = value
		}
	}

	if len(setFields) > 0 {
		update["$set"] = setFields
	}
	if len(unsetFields) > 0 {
		update["$unset"] = unsetFields
	}

	if len(update) == 0 {
		return nil
	}

	opts := options.Update().SetUpsert(false)
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		logger.Log.Error("带状态检查的选择性更新视频失败", logger.Error(err), logger.String("id", id.Hex()), logger.String("expectedStatus", expectedStatus))
		return fmt.Errorf("仓库层: 带状态检查的选择性更新视频失败: %w", err)
	}

	if result.MatchedCount == 0 {
		// 可能是视频不存在，或者状态已经改变
		var existingVideo Video
		// 使用传入的context（可能是SessionContext）
		err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&existingVideo)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return ErrVideoNotFound
			}
			return fmt.Errorf("检查视频状态失败: %w", err)
		}
		// 视频存在但状态不匹配
		return fmt.Errorf("视频状态已改变，期望: %s，实际: %s", expectedStatus, existingVideo.Status)
	}

	return nil
}

// FindOneAndUpdateToProcessing 原子性地查找一个 "Pending" 状态的视频，
// 将其状态更新为 "Processing"，并返回更新后的文档。
// 如果没有找到待处理的视频，将返回 mongo.ErrNoDocuments 错误。
func (r *repository) FindOneAndUpdateToProcessing(ctx context.Context) (*Video, error) {
	filter := bson.M{"st": StatusPending}
	update := bson.M{
		"$set": bson.M{
			"st": StatusProcessing,
			// 移除手动时间戳，gomongo会自动更新_mt
		},
	}

	// 设置选项，要求返回更新后的文档
	opts := options.FindOneAndUpdate().SetReturnDocument(options.After)

	var updatedVideo Video
	// 使用传入的context（可能是SessionContext）
	err := r.collection.FindOneAndUpdate(ctx, filter, update, opts).Decode(&updatedVideo)
	if err != nil {
		// 如果没有找到文档，这是一个正常情况，不是一个需要记录日志的错误
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
		logger.Log.Error("原子更新视频状态为 'Processing' 失败", logger.Error(err))
		return nil, fmt.Errorf("仓库层: FindOneAndUpdateToProcessing 失败: %w", err)
	}

	return &updatedVideo, nil
}

// UpdateCategory 在一个事务中更新所有匹配旧分类ID的视频为新分类ID
func (r *repository) UpdateCategory(ctx context.Context, oldCategoryID, newCategoryID primitive.ObjectID) error {
	filter := bson.M{"catId": oldCategoryID}
	update := bson.M{"$set": bson.M{"catId": newCategoryID}}
	// 移除手动时间戳，gomongo会自动更新_mt

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		logger.Log.Error("批量更新视频分类失败",
			logger.Error(err),
			logger.String("oldCategoryID", oldCategoryID.Hex()),
			logger.String("newCategoryID", newCategoryID.Hex()),
		)
		return fmt.Errorf("仓库层: 批量更新视频分类失败: %w", err)
	}

	logger.Log.Info("成功批量更新视频分类",
		logger.Int64("updatedCount", result.ModifiedCount),
		logger.String("oldCategoryID", oldCategoryID.Hex()),
		logger.String("newCategoryID", newCategoryID.Hex()),
	)
	return nil
}

// UpdateAdvertiser 在一个事务中更新所有匹配旧广告主ID的视频为新广告主ID
func (r *repository) UpdateAdvertiser(ctx context.Context, oldAdvertiserID, newAdvertiserID primitive.ObjectID) (int64, error) {
	filter := bson.M{"ClntId": oldAdvertiserID}
	update := bson.M{"$set": bson.M{"ClntId": newAdvertiserID}}
	// 移除手动时间戳，gomongo会自动更新_mt

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		logger.Log.Error("批量更新视频所属广告主失败",
			logger.Error(err),
			logger.String("oldAdvertiserID", oldAdvertiserID.Hex()),
			logger.String("newAdvertiserID", newAdvertiserID.Hex()),
		)
		return 0, fmt.Errorf("仓库层: 批量更新视频所属广告主失败: %w", err)
	}

	logger.Log.Info("成功批量更新视频所属广告主",
		logger.Int64("updatedCount", result.ModifiedCount),
		logger.String("oldAdvertiserID", oldAdvertiserID.Hex()),
		logger.String("newAdvertiserID", newAdvertiserID.Hex()),
	)
	return result.ModifiedCount, nil
}

// IncrementStats 原子性地增加视频的统计数据，并重新计算完成率
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) IncrementStats(ctx context.Context, videoID string, views, likes, collections, completions int64) error {
	objectID, err := primitive.ObjectIDFromHex(videoID)
	if err != nil {
		return fmt.Errorf("无效的视频ID: %w", err)
	}

	filter := bson.M{"_id": objectID}

	// 使用$inc操作符进行原子性增量更新
	update := bson.M{
		"$inc": bson.M{
			"stat.vws":     views,
			"stat.lks":     likes,
			"stat.cltsCnt": collections,
			"stat.cplCnt":  completions,
		},
	}

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		logger.Log.Error("原子性更新视频统计失败", logger.Error(err), logger.String("videoID", videoID))
		return fmt.Errorf("仓库层: 原子性更新视频统计失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return ErrVideoNotFound
	}

	// 更新完成率
	if err := r.updateCompletionRate(ctx, objectID); err != nil {
		logger.Log.Error("更新完成率失败", logger.Error(err), logger.String("videoID", videoID))
		// 不返回错误，因为统计数据已经更新成功
	}

	return nil
}

// updateCompletionRate 更新视频的完成率
func (r *repository) updateCompletionRate(ctx context.Context, videoID primitive.ObjectID) error {
	// 使用聚合管道计算完成率
	pipeline := mongo.Pipeline{
		{
			{Key: "$match", Value: bson.M{"_id": videoID}},
		},
		{
			{Key: "$set", Value: bson.M{
				"stat.cplRt": bson.M{
					"$cond": bson.M{
						"if":   bson.M{"$eq": []interface{}{"$stat.vws", 0}},
						"then": "0.0%",
						"else": bson.M{
							"$concat": []interface{}{
								bson.M{"$toString": bson.M{
									"$round": []interface{}{
										bson.M{"$multiply": []interface{}{
											bson.M{"$divide": []interface{}{"$stat.cplCnt", "$stat.vws"}},
											100,
										}},
										1,
									},
								}},
								"%",
							},
						},
					},
				},
			}},
		},
		{
			{Key: "$merge", Value: bson.M{
				"into":        "video_videos",
				"whenMatched": "replace",
			}},
		},
	}

	_, err := r.collection.Aggregate(ctx, pipeline)
	return err
}

// Find 使用聚合管道根据过滤条件查找和分页视频
func (r *repository) Find(ctx context.Context, filter VideoFilter) ([]Video, int64, error) {
	pipeline := mongo.Pipeline{}

	// 1. 构建动态的 $match 阶段
	matchStage := bson.D{}
	if filter.CategoryID != nil {
		matchStage = append(matchStage, bson.E{Key: "catId", Value: filter.CategoryID})
	}
	if filter.FilterForNullClient {
		matchStage = append(matchStage, bson.E{Key: "ClntId", Value: nil})
	} else if filter.ClientID != nil {
		matchStage = append(matchStage, bson.E{Key: "ClntId", Value: filter.ClientID})
	}
	if len(filter.Status) > 0 {
		matchStage = append(matchStage, bson.E{Key: "st", Value: bson.M{"$in": filter.Status}})
	}

	// 直接在 'pts' (publishedAt) 字段上进行时间过滤
	dateFilter := bson.D{}
	if filter.FromDate != nil {
		dateFilter = append(dateFilter, bson.E{Key: "$gte", Value: filter.FromDate})
	}
	if filter.ToDate != nil {
		dateFilter = append(dateFilter, bson.E{Key: "$lte", Value: filter.ToDate})
	}
	if len(dateFilter) > 0 {
		// 只有已发布的视频才有 'pts' 字段，这个过滤是合理的
		matchStage = append(matchStage, bson.E{Key: "pts", Value: dateFilter})
	}

	if len(matchStage) > 0 {
		pipeline = append(pipeline, bson.D{{Key: "$match", Value: matchStage}})
	}

	// 2. 使用 $facet 实现分页和总数统计
	facetStage := bson.D{
		{Key: "$facet", Value: bson.D{
			{Key: "metadata", Value: bson.A{
				bson.D{{Key: "$count", Value: "total"}},
			}},
			{Key: "data", Value: bson.A{
				// 主要排序依据 pts, 其次是 _mt, _ts（gomongo自动时间戳），以处理 pts 为空的草稿等
				bson.D{{Key: "$sort", Value: bson.D{
					{Key: "pts", Value: -1},
					{Key: "_mt", Value: -1},
					{Key: "_ts", Value: -1},
				}}},
				bson.D{{Key: "$skip", Value: (filter.Page - 1) * filter.Limit}},
				bson.D{{Key: "$limit", Value: filter.Limit}},
				bson.D{{Key: "$addFields", Value: bson.D{
					{Key: "stat.cplRt", Value: bson.M{"$toString": "$stat.cplRt"}},
				}}},
			}},
		}},
	}
	pipeline = append(pipeline, facetStage)

	// 执行聚合查询
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// 解析聚合结果: $facet返回的是一个包含数组的单一文档，而不是文档数组。
	// 因此，我们解码到单个结构体而不是结构体切片。
	var results struct {
		Metadata []struct {
			Total int64 `bson:"total"`
		} `bson:"metadata"`
		Data []Video `bson:"data"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&results); err != nil {
			return nil, 0, err
		}
	}

	if len(results.Metadata) == 0 {
		return []Video{}, 0, nil
	}

	total := results.Metadata[0].Total
	videos := results.Data

	return videos, total, nil
}

// DeleteByID 删除指定ID的视频文档
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) DeleteByID(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		logger.Log.Error("删除视频文档失败", logger.Error(err), logger.String("id", id.Hex()))
		return fmt.Errorf("仓库层: 删除视频失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return ErrVideoNotFound
	}

	return nil
}

// GetAggregatedStats calculates aggregated statistics for videos based on a filter.
func (r *repository) GetAggregatedStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error) {
	matchStage := bson.D{}
	if filter.CategoryID != nil {
		matchStage = append(matchStage, bson.E{Key: "catId", Value: filter.CategoryID})
	}
	if filter.FilterForNullClient {
		matchStage = append(matchStage, bson.E{Key: "ClntId", Value: nil})
	} else if filter.ClientID != nil {
		matchStage = append(matchStage, bson.E{Key: "ClntId", Value: filter.ClientID})
	}
	if len(filter.Status) > 0 {
		matchStage = append(matchStage, bson.E{Key: "st", Value: bson.M{"$in": filter.Status}})
	}

	// Directly filter on 'pts'
	dateFilter := bson.D{}
	if filter.FromDate != nil {
		dateFilter = append(dateFilter, bson.E{Key: "$gte", Value: *filter.FromDate})
	}
	if filter.ToDate != nil {
		dateFilter = append(dateFilter, bson.E{Key: "$lte", Value: *filter.ToDate})
	}
	if len(dateFilter) > 0 {
		matchStage = append(matchStage, bson.E{Key: "pts", Value: dateFilter})
	}

	pipeline := mongo.Pipeline{}
	if len(matchStage) > 0 {
		pipeline = append(pipeline, bson.D{{Key: "$match", Value: matchStage}})
	}

	groupStage := bson.D{
		{Key: "_id", Value: nil},
		{Key: "totalVideos", Value: bson.D{{Key: "$sum", Value: 1}}},
		{Key: "totalViews", Value: bson.D{{Key: "$sum", Value: "$stat.vws"}}},
		{Key: "totalLikes", Value: bson.D{{Key: "$sum", Value: "$stat.lks"}}},
		{Key: "totalCollections", Value: bson.D{{Key: "$sum", Value: "$stat.cltsCnt"}}},
		{Key: "totalCompletions", Value: bson.D{{Key: "$sum", Value: "$stat.cplCnt"}}},
	}
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: groupStage}})

	// Final projection stage
	projectStage := bson.D{
		{Key: "_id", Value: 0},
		{Key: "totalVideos", Value: 1},
		{Key: "totalViews", Value: 1},
		{Key: "totalLikes", Value: 1},
		{Key: "totalCollections", Value: 1},
		{Key: "overallCompletionRate", Value: bson.M{
			"$cond": bson.A{
				bson.M{"$eq": bson.A{"$totalViews", 0}},
				"0.00%",
				bson.M{"$concat": []interface{}{
					bson.M{"$toString": bson.M{
						"$round": bson.A{
							bson.M{"$multiply": []interface{}{
								bson.M{"$divide": bson.A{"$totalCompletions", "$totalViews"}},
								100,
							}},
							2,
						},
					}},
					"%",
				}},
			},
		}},
	}
	pipeline = append(pipeline, bson.D{{Key: "$project", Value: projectStage}})

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("视频统计聚合查询失败: %w", err)
	}
	defer cursor.Close(ctx)

	var results []VideoStatsSummary
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("解码视频统计结果失败: %w", err)
	}

	if len(results) == 0 {
		return &VideoStatsSummary{
			OverallCompletionRate: "0.00%",
		}, nil
	}

	return &results[0], nil
}
